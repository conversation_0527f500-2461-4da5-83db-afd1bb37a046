import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, __dirname); // 获取环境变量

  let staticBase = "https://static-kr.kyxy777.com";
  let base = "/";

  if (command === "build") {
    base = env.VITE_MODE === "test" ? `${staticBase}/h5sdk.xjxztw.com/dist/` : `${staticBase}/pay.knetgame.com/dist/`;
  }

  return {
    plugins: [vue(), vueJsx()],
    base,
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
        components: path.resolve(__dirname, "src/components"),
        // 添加更多别名
      },
    },
    build: {
      sourcemap: env.VITE_MODE !== "prod",
    },
    server: {
      host: true, // 或者设置为 '0.0.0.0'
      proxy: {
        "/api": {
          target: env.VITE_PROXY_URL, // 代理目标地址
          changeOrigin: true, // 是否更改原始主机头为目标URL
          rewrite: (path) => path.replace(/^\/api/, ""), // 重写路径
        },
      },
    },
    css: {
      postcss: {
        plugins: [],
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          // 其他 less 配置
        },
      },
    },
  };
});
