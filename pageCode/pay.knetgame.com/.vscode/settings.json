{"[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "explorer.confirmDragAndDrop": false, "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always", "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "workbench.startupEditor": "none", "diffEditor.ignoreTrimWhitespace": false, "files.autoSave": "after<PERSON>elay", "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.singleQuote": false, "prettier.semi": true, "prettier.trailingComma": "es5", "prettier.tabWidth": 2, "prettier.useTabs": false, "i18n-ally.localesPaths": ["src/i18n/locales"], "i18n-ally.keystyle": "nested"}