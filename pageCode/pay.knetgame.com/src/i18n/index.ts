import { createI18n } from "vue-i18n";
import EN from "./locales/EN.json";
import TW from "./locales/TW.json";
import CN from "./locales/CN.json";
import VN from "./locales/VN.json";
import KR from "./locales/KR.json";
import JP from "./locales/JP.json";
import TH from "./locales/TH.json";

// 语言包
const langs = { EN, TW, CN, VN, KR, JP, TH };

export type language = keyof typeof langs;

// 支持的语言列表
export const SUPPORTED_LANGUAGES = Object.keys(langs);

// 获取语言设置
export function detectLanguage(): string {
  // 优先从用户主动设置获取
  if (localStorage.getItem("language")) {
    return localStorage.getItem("language") as string;
  }
  // 1. 从URL参数获取
  const urlParams = new URLSearchParams(window.location.search);
  const langParam = urlParams.get("language");
  if (langParam && SUPPORTED_LANGUAGES.includes(langParam.toUpperCase())) {
    return langParam.toUpperCase();
  }

  // 2. 从浏览器语言获取
  const browserLang = navigator.language;
  if (browserLang) {
    const langCode = browserLang.split("-")[0].toUpperCase();
    const regionCode = browserLang.split("-")[1]?.toUpperCase();

    // 检查是否是支持的语言
    if (SUPPORTED_LANGUAGES.includes(langCode)) {
      return langCode;
    }

    // 处理中文地区（zh-CN, zh-TW等）
    if (langCode === "ZH") {
      if (regionCode === "TW" || regionCode === "HK") {
        return "TW";
      }
      return "CN";
    }
  }

  // 3. 默认为英文
  return "EN";
}

const i18n = createI18n({
  locale: detectLanguage(),
  globalInjection: true, //全局配置$t
  legacy: false,
  messages: langs,
});

export default i18n;
