import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory("/"),
  routes: [
    {
      path: "/",
      name: "/",
      meta: {
        title: "Pay",
      },
      component: () => import("../views/Home/index.vue"),
    },
    {
      path: "/pay/result",
      name: "/result",
      meta: {
        title: "PayResult!",
      },
      component: () => import("../views/Result.vue"),
    },
  ],
});

router.beforeEach((to, _, next) => {
  document.title = to.meta.title as string;
  next();
});

export default router;
