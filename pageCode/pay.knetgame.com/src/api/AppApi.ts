import { <PERSON><PERSON><PERSON> } from "./base/BaseApi";
import type { IApiResult } from "./viewModel/IApiResult";
export interface ILoginParams {
  email: string;
  pass: string;
}

export interface IGoogleLoginParams {
  access_token: string;
}

export interface IFacebookLoginParams {
  access_token: string;
}

export interface IRoleParams {
  user_id: string;
  server_id?: string;
}

export interface IRoleInfo {
  role_id: string;
  role_name: string;
  server_id: string;
  server_name: string;
  role_level: string;
}

export interface IServerInfo {
  server_id: string;
  server_name: string;
}

export interface IUserInfo {
  user_id: string;
  user_name: string;
  user_auth_token: string;
  country: string;
  continent: string;
}

export interface IGoodsParams {
  pay_code: string;
}

export interface IGoodsItem {
  item_code: string;
  item_name: string;
  item_desc: string;
  usd_money: string;
  currency: string;
  item_money: string;
}

export interface IPaymentMethod {
  payment_type: string;
  payment_code: string;
  icon: string;
  name: string;
  action: string;
  package_name: string;
  unique: string;
}

export interface IPaymentListResult {
  region: string;
  region_name: string;
  payments: IPaymentMethod[];
}

export type IPaymentListResultArray = IPaymentListResult[];

export interface IPayRecordParams {
  user_id: string;
  page: string;
  page_size: string;
}

export interface IPayRecordItem {
  pay_time: string;
  status: number;
  order_id: string;
  product_name: string;
  money: string;
  currency: string;
  channel: string;
  channel_name: string;
  user_side_display_name: string;
}

export interface IPayRecordResult {
  data: IPayRecordItem[];
  total: number;
}

export interface IOrderParams {
  user_id: string;
  server_id?: string;
  role_id: string;
  product_id: string;
  role_name: string;
  action: string;
  package_name: string;
  payment_code?: string;
  jump_url: string;
}

export interface IPayData {
  extra_data: any;
  notify_url: string;
  order_id: string;
  pay_url: string;
  payment_url: string;
}

export interface ISendOrderResult {
  payment_url: string;
  pay_data: IPayData;
}

export default class AppApi extends BaseApi {
  /**
   * 检测订单状态
   * @param {*} params
   * @returns
   */
  static checkOrderStatus(url: string, params: any): any {
    return super.get(url, params);
  }

  /**
   * 确认订单
   * @param {*} params
   * @returns
   */
  static capture(url: string, params: any) {
    return super.post(url, params);
  }

  // 初始化获取数据
  static getInitData(): Promise<IApiResult<any>> {
    return super.get("/webpay/webV2/init");
  }

  // 邮箱登录
  static login(data: ILoginParams): Promise<IApiResult<IUserInfo>> {
    return super.post("/webpay/webV2/login", data);
  }

  // google登录
  static googleLogin(data: IGoogleLoginParams): Promise<IApiResult<IUserInfo>> {
    return super.post("/webpay/webV2/googleAuthorize", data);
  }

  // facebook登录
  static facebookLogin(data: IFacebookLoginParams): Promise<IApiResult<IUserInfo>> {
    return super.post("/webpay/webV2/facebookAuthorize", data);
  }

  // 获取区服列表
  static getServerList(): Promise<IApiResult<IServerInfo[]>> {
    return super.get("/webpay/webV2/serverList");
  }

  // 获取角色列表
  static getRoleList(params: IRoleParams): Promise<IApiResult<IRoleInfo[]>> {
    return super.get("/webpay/webV2/roleList", params);
  }

  // 获取支付方式列表
  static getPaymentList(): Promise<IApiResult<IPaymentListResult[]>> {
    return super.get("/webpay/webV2/payWithList");
  }
  // 获取商品列表
  static getGoodsList(params: IGoodsParams): Promise<IApiResult<IGoodsItem[]>> {
    return super.get("/webpay/webV2/productList", params);
  }

  // 获取充值记录
  static getPayRecord(params: IPayRecordParams): Promise<IApiResult<IPayRecordResult>> {
    return super.get("webpay/webV2/historicalPayLogList", params);
  }

  // 下单
  static sendOrder(data: IOrderParams): Promise<IApiResult<ISendOrderResult>> {
    return super.post("webpay/webV2/order", data);
  }
}
