import "./assets/main.css";
import i18n from "./i18n/index";

import { createApp } from "vue";
import { createPinia } from "pinia";
import PiniaPersistedState from "pinia-plugin-persistedstate";
import App from "./App.vue";
import router from "./router";

const app = createApp(App);

const pinia = createPinia();

// 使用持久化插件
pinia.use(PiniaPersistedState);

app.use(pinia);
app.use(router);
app.use(i18n);

app.mount("#app");
