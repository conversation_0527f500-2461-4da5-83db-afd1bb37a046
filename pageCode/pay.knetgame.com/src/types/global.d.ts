// Google Identity Services 类型声明
declare global {
  interface Window {
    androidJs: any;
    paypal: any;
    ApplePaySession: any;
    google: any;
    google?: {
      accounts?: {
        oauth2: {
          initTokenClient: (config: { client_id: string; scope: string; callback: (response: any) => void }) => {
            requestAccessToken: () => void;
          };
        };
      };
    };

    // Facebook 类型声明
    FB?: {
      init: (config: { appId: string; cookie: boolean; xfbml: boolean; version: string }) => void;
      login: (callback: (response: any) => void, options: { scope: string; return_scopes: boolean }) => void;
      api: (path: string, params: any, callback: (response: any) => void) => void;
    };
  }
}

export {};
