import type { IUserInfo } from "@/api/AppApi";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useAppStore = defineStore(
  "app",
  () => {
    const isShowLoading = ref(false);
    const initData = ref<any>(null);
    const userInfo = ref<IUserInfo | null>(null);

    function setInitData(data: any) {
      initData.value = data;
    }

    function setUserInfo(data: any) {
      userInfo.value = data;
    }

    return {
      isShowLoading,
      initData,
      userInfo,
      setInitData,
      setUserInfo,
    };
  },
  {
    persist: {
      key: "app_store", // 自定义存储名称
      storage: localStorage, // 使用 localStorage 或 sessionStorage
    },
  }
);
