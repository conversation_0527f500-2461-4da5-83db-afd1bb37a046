<template>
  <Result :status="status" :title="t('message.pay_res')" :subTitle="showText">
    <template #extra>
      <Button type="primary" @click="back">{{ t("message.back") }}</Button>
    </template>
  </Result>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { computed, onMounted, ref } from "vue";
import AppUtils from "@/utils/AppUtils";
import AppApi from "@/api/AppApi";
import { Button, Result } from "ant-design-vue";

const route = useRoute();
const { t, locale } = useI18n();
const return_status = ref(decodeURIComponent(route.query.return_status as string));
const fail_message = ref(decodeURIComponent((route.query.fail_message as string) || ""));
const query_order_url = ref(decodeURIComponent(route.query.query_order_url as string));
const web_payment_url = ref(decodeURIComponent((route.query.web_payment_url as string) || ""));
const order_id = ref(route.query.order_id);
const token = ref(route.query.token);

const returnStatus = ["success", "fail", "unknow"];
const status = computed(() => {
  switch (return_status.value) {
    case returnStatus[0]:
      return returnStatus[0];
    case returnStatus[1]:
      return "error";
    case returnStatus[2]:
      return "info";
  }
});

const showText = computed(() => {
  if (return_status.value == "unknow") {
    return t("message.processingPayment");
  } else if (return_status.value == "fail") {
    return fail_message.value;
  }
  return "";
});

const back = () => {
  //webview android
  if (window.androidJs) {
    return window.androidJs.close();
  }
  location.href = web_payment_url.value as string;
};

const checkOrderStatus = async () => {
  setTimeout(async () => {
    if (query_order_url.value) {
      const result = await AppApi.checkOrderStatus(query_order_url.value, {
        order_id: order_id.value,
        token: token.value,
      });
      switch (result.data.status) {
        case 1:
          return_status.value = "success";
          break;
        case 2:
          return_status.value = "fail";
          break;
        case 0:
          return_status.value = "unknow";
          break;
      }
      if (return_status.value == "unknow") {
        checkOrderStatus();
      }
    }
  }, 3000);
};

onMounted(() => {
  if (return_status.value == "unknow") {
    checkOrderStatus();
  }
  locale.value = AppUtils.getBrowserLangCode();
});
</script>
