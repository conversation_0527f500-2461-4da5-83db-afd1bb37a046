<script setup lang="ts">
import { Select } from "ant-design-vue";
import { onMounted, ref, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import Login from "@/views/Login.vue";
import Sidebar from "./Components/Sidebar.vue";
import Header from "./Components/Header.vue";
import PayRecord from "./Components/PayRecord.vue";
import Footer from "./Components/Footer.vue";
import { useResizeHandler } from "@/hooks/useResizeHandler";
import { useIsMobile } from "@/hooks/useIsMobile";
import AppApi, { type IRoleInfo, type IServerInfo, type IUserInfo, type IGoodsItem, type IGoodsParams, type IPaymentMethod, type IPaymentListResult, type IOrderParams } from "@/api/AppApi";
import { ApiResultCode } from "@/api/viewModel/IApiResultCode";
import { useAppStore } from "@/stores/app";
import { usePaypal } from "@/hooks/usePaypal";
import { message } from "ant-design-vue";

useResizeHandler();

const { isMobile } = useIsMobile();
const { t } = useI18n();
const appStore = useAppStore();
const { initPaypal, updateTransactionInfo, isSupportApplePay } = usePaypal();

// 支付类型常量
const PAYMENT_TYPES = {
  PAYPAL_APPLEPAY: "paypal_applepay",
  PAYPAL_GOOGLEPAY: "paypal_googlepay",
} as const;

interface IFormData {
  server: IServerInfo | null;
  role: IRoleInfo | null;
}
const fromData = ref<IFormData>({
  server: null,
  role: null,
});

const selectTabs = ref(1);
const showPopMorePayBox = ref(false);
const selectedGoodsItem = ref<IGoodsItem | null>(null);

const loginRef = ref<InstanceType<typeof Login> | null>(null);

const goodsList = ref<IGoodsItem[]>([]);

const serverOptions = ref<IServerInfo[]>([]);
const roleOptions = ref<IRoleInfo[]>([]);

const paymentList = ref<IPaymentListResult[]>([]);
const currentRegion = ref<string>("global");
const selectedPayment = ref<IPaymentMethod | null>(null);

// 地区标签
const regionTabs = computed(() => {
  if (!paymentList.value.length) return [];
  return paymentList.value.map((region) => ({
    region: region.region,
    region_name: region.region_name,
  }));
});

// 当前地区的支付方式列表
const currentPayments = computed(() => {
  if (!paymentList.value.length) return [];
  const currentRegionData = paymentList.value.find((p) => p.region === currentRegion.value);
  return currentRegionData?.payments || [];
});

// 计算是否可以提交
const canSubmit = computed(() => {
  return !!(selectedGoodsItem.value && selectedPayment.value && fromData.value.role);
});

// 支付方式是否是PayPal子支付
const isPayPal = computed(() => {
  return Object.values(PAYMENT_TYPES).includes(selectedPayment.value?.payment_code as any);
});

// 初始化数据
async function getInitData() {
  try {
    const res = await AppApi.getInitData();
    if (res.code === ApiResultCode.OK) {
      appStore.setInitData(res.data);
      // 获取支付方式列表
      getPaymentList();
      if (appStore.userInfo) initServerAndRoleList();
      // 初始化PayPal
      initPayPalConfig();
    }
  } catch (error) {
    console.error(error);
  }
}

// 初始化区服和角色列表
function initServerAndRoleList() {
  // 是否需要区服
  if (appStore.initData?.need_server_list) {
    getServerList();
  } else {
    getRoleList();
  }
}

// 获取区服列表
async function getServerList() {
  try {
    const res = await AppApi.getServerList();
    if (res.code === ApiResultCode.OK && res.data) {
      serverOptions.value = res.data;
      // 如果用户已登录且有区服列表，默认选择第一个区服
      if (appStore.userInfo && serverOptions.value.length > 0) {
        fromData.value.server = serverOptions.value[0];
        getRoleList(serverOptions.value[0].server_id);
      }
    }
  } catch (error) {
    console.error(error);
  }
}

// 获取角色列表
async function getRoleList(serverId?: string) {
  try {
    if (!appStore.userInfo) {
      return;
    }
    const res = await AppApi.getRoleList({ user_id: appStore.userInfo.user_id, server_id: serverId });
    if (res.code === ApiResultCode.OK && res.data) {
      roleOptions.value = res.data;
      // 如果用户已登录且有角色列表，默认选择第一个角色
      if (appStore.userInfo && roleOptions.value.length > 0) {
        fromData.value.role = roleOptions.value[0];
      }
    }
  } catch (error) {
    console.error(error);
  }
}

// 获取商品列表
async function getGoodsList(pay_code: string) {
  try {
    const params: IGoodsParams = {
      pay_code,
    };
    const res = await AppApi.getGoodsList(params);
    if (res.code === ApiResultCode.OK && res.data) {
      goodsList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
}

// 获取支付方式列表
async function getPaymentList() {
  try {
    const res = await AppApi.getPaymentList();
    if (res.code === ApiResultCode.OK && res.data) {
      paymentList.value = res.data;
      // 默认选择第一个地区的第一个支付方式
      if (res.data.length > 0) {
        const firstPayment = res.data[0].payments[0];
        currentRegion.value = res.data[0].region;
        handlePaymentSelect(firstPayment);
      }
    }
  } catch (error) {
    console.error(error);
  }
}

// 打开登录弹窗
const openLogin = () => {
  loginRef.value?.openLoginPopup();
};

// 登录成功回调
const handleLoginSuccess = (data: IUserInfo) => {
  initServerAndRoleList();
};

// 服务器选择处理
const handleServerSelect = (serverId: string) => {
  const server = serverOptions.value.find((option) => option.server_id === serverId);
  if (server) {
    fromData.value.server = server;
    // 清除角色选择
    fromData.value.role = null;
    getRoleList(server.server_id);
  }
};

// 角色选择处理
const handleRoleSelect = (roleId: string) => {
  const role = roleOptions.value.find((option) => option.role_id === roleId);
  if (role) {
    fromData.value.role = role;
  }
};

// 支付方式选择处理
const handlePaymentSelect = (payment: IPaymentMethod) => {
  // 如果选择的是Apple Pay，检查是否支持
  if (payment.payment_code === PAYMENT_TYPES.PAYPAL_APPLEPAY && !isSupportApplePay.value) {
    message.error(t("message.applePayRequirement"));
    return;
  }

  selectedPayment.value = payment;
  getGoodsList(payment.payment_code);
};

// 商品选择处理
const handleGoodsSelect = (item: IGoodsItem) => {
  selectedGoodsItem.value = item;
  // 更新商品信息到PayPal
  updateTransactionInfo({
    totalPrice: item.item_money,
    currencyCode: item.currency,
  });
};

// 创建订单
const createOrder = async () => {
  // 检查用户登录状态
  if (!appStore.userInfo) {
    openLogin();
    return;
  }

  try {
    // 构建订单参数
    const { action, package_name, payment_code } = selectedPayment.value!;
    const { role_id, role_name } = fromData.value.role!;
    const orderParams: IOrderParams = {
      role_id,
      role_name,
      action,
      // package_name: "com.test.weben.paypal", // 测试用包名
      package_name,
      payment_code,
      user_id: appStore.userInfo.user_id,
      server_id: fromData.value.server?.server_id,
      product_id: selectedGoodsItem.value!.item_code,
      jump_url: `${location.origin}/pay/result${location.search}`,
    };

    const res = await AppApi.sendOrder(orderParams);
    if (res.code === ApiResultCode.OK && res.data?.payment_url) {
      return res.data;
    } else {
      console.error("下单失败:", res.message);
      message.error(res.message);
      return null;
    }
  } catch (error) {
    console.error("下单过程中发生错误:", error);
    return null;
  }
};

// 下单处理
const handleCreateOrder = async () => {
  const data = await createOrder();
  if (!data) return;
  window.open(data.payment_url, "_blank");
};

// 处理换行符转HTML
const formatLineBreaks = (text: string | undefined) => {
  if (!text) return "";
  return text.replace(/\n/g, "<br>");
};

/** 初始化PayPal相关配置 */
function initPayPalConfig() {
  const FUNDING_SOURCES = [
    {
      pay: "applepay",
      btnName: "applepay-button-container",
    },
    {
      pay: "googlepay",
      btnName: "googlepay-button-container",
      btnOptions: {
        buttonColor: "default",
        buttonType: "buy",
        buttonRadius: 100,
        buttonBorderType: "default_border",
        buttonSizeMode: "fill",
      },
    },
    { pay: "paypal", btnName: "paypal-button-container" },
  ];

  const payClick = async (data: unknown) => {
    console.log(data);
    const res = await createOrder();
    if (!res) return {};
    const { notify_url, order_id } = res.pay_data;
    const extra_data = JSON.parse(res.pay_data.extra_data.data);
    return {
      paypal_order_id: extra_data.id,
      notify_url,
      order_id,
    };
  };

  const { paypal_client_id, paypal_merchant_id } = appStore.initData.official_payment_config;

  const test = {
    clientId: "AYiM0LKgoXjhKYQ5snV2nEkqFEkl3vphu3A4DVo_C2THWFi5hfmnLPFBWvqI0iQzVjtNMS1k3OTR-VQr",
    merchantId: "NPHDYC2J983TC",
  };

  const initParams = {
    clientId: paypal_client_id || test.clientId,
    merchantId: paypal_merchant_id || test.merchantId,
    components: "buttons,applepay,googlepay",
    currency: selectedGoodsItem.value?.currency || "USD",
    locale: "en_US",
  };
  initPaypal(initParams, FUNDING_SOURCES, payClick);
}

getInitData();

onMounted(() => {
  // 检查用户是否登录
  watch(
    () => appStore.userInfo,
    (userInfo: IUserInfo | null) => {
      if (!userInfo) openLogin();
    },
    { immediate: true }
  );
});
</script>

<template>
  <div class="main">
    <Header :selectTabs="selectTabs" @update:selectTabs="selectTabs = $event" @openLogin="openLogin" />
    <div class="stored-value-hall" v-if="selectTabs == 1">
      <div class="p1">
        <div class="title">{{ t("message.selectRole") }}</div>
        <div class="game-icon">
          <img :src="appStore.initData?.icon" :alt="appStore.initData?.user_side_display_name" />
        </div>
        <div class="select-box">
          <div class="select-item">
            <div class="label">{{ t("message.game") }}:</div>
            <div class="game-name">{{ appStore.initData?.user_side_display_name }}</div>
          </div>
          <div class="select-item" v-if="appStore.initData?.need_server_list">
            <div class="label">{{ t("message.server") }}:</div>
            <Select :value="fromData.server?.server_id" @change="handleServerSelect">
              <Select.Option v-for="option in serverOptions" :value="option.server_id">{{ option.server_name }}</Select.Option>
            </Select>
          </div>
          <div class="select-item">
            <div class="label">{{ t("message.role") }}:</div>
            <Select :value="fromData.role?.role_id" @change="handleRoleSelect">
              <Select.Option v-for="option in roleOptions" :value="option.role_id">{{ option.role_name }}</Select.Option>
            </Select>
          </div>
        </div>
        <div class="store-buttons">
          <a v-if="appStore.initData?.google_download_url" :href="appStore.initData?.google_download_url" target="_blank" class="store-btn google-play">
            <img src="@/assets/imgs/pc/google-store.png" alt="Google Play" />
          </a>
          <a v-if="appStore.initData?.ios_download_url" :href="appStore.initData?.ios_download_url" target="_blank" class="store-btn apple-store">
            <img src="@/assets/imgs/pc/apple-store.png" alt="App Store" />
          </a>
        </div>
      </div>
      <div class="p2">
        <div class="title">{{ t("message.paymentMethods") }}</div>
        <div class="more-pay" @click="showPopMorePayBox = !showPopMorePayBox">{{ t("message.morePaymentOptions") }}&gt;</div>
        <div class="pop-more-pay-box" v-if="showPopMorePayBox">
          <div class="pop-area-list">
            <div v-for="region in regionTabs" :key="region.region" class="pop-area-item" :class="{ active: currentRegion === region.region }" @click="currentRegion = region.region">
              {{ region.region_name }}
            </div>
          </div>
          <div class="pop-pay-list">
            <div v-for="(payment, index) in currentPayments" :key="index" class="pop-pay-item" :class="{ active: selectedPayment?.unique === payment.unique }" @click="handlePaymentSelect(payment)">
              <img :src="payment.icon" :alt="payment.name" />
            </div>
          </div>
        </div>
        <div class="tabs">
          <div v-for="tab in regionTabs" :key="tab.region" class="tab" :class="{ active: currentRegion === tab.region }" @click="currentRegion = tab.region">
            {{ tab.region_name }}
          </div>
        </div>
        <div class="p2-content-box">
          <div class="pay-list">
            <div v-for="(payment, index) in currentPayments" :key="index" class="pay-item" :class="{ active: selectedPayment?.unique === payment.unique }" @click="handlePaymentSelect(payment)">
              <img :src="payment.icon" :alt="payment.name" />
            </div>
          </div>
        </div>
      </div>
      <div class="p3">
        <div class="title">{{ t("message.selectPaymentItems") }}</div>
        <div class="goods-classify">{{ t("message.selectPaymentItems") }}</div>
        <div class="goods-list">
          <div v-for="item in goodsList" :key="item.item_code" class="goods-item" :class="{ active: selectedGoodsItem?.item_code === item.item_code }" @click="handleGoodsSelect(item)">
            <div class="goods-img-box">
              <div class="goods-img">
                <img src="@/assets/imgs/pc/gift-box.png" />
              </div>
            </div>
            <div class="goods-info-box">
              <div class="top-box">
                <div class="goods-title" :title="item.item_name">{{ item.item_name || item.item_desc }}</div>
                <div class="goods-price">{{ item.item_money }} {{ item.currency }}</div>
              </div>
              <div class="goods-desc">{{ item.item_desc }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 充值记录 -->
    <PayRecord v-if="selectTabs == 2" />

    <div class="tips-box">
      <div class="pay-desc" v-if="selectTabs == 1 || isMobile" v-html="formatLineBreaks(appStore.initData?.web_payment_rules_description)"></div>
      <div class="declare">
        <div v-html="formatLineBreaks(appStore.initData?.web_payment_footer_description)"></div>
        <div class="item">
          ※
          <a class="blue-font" :href="appStore.initData?.service_terms_url" target="_blank">{{ t("message.termsOfService") }}</a> |
          <a class="blue-font" :href="appStore.initData?.privacy_agreement_url" target="_blank">{{ t("message.privacyPolicy") }}</a>
        </div>
      </div>
    </div>

    <Footer :selectTabs="selectTabs" :selectedGoodsItem="selectedGoodsItem" :isDisabled="!canSubmit" :isPayPal="isPayPal" :selectedPayment="selectedPayment" @submit="handleCreateOrder" />
    <Sidebar v-if="!isMobile" />

    <Login ref="loginRef" @loginSuccess="handleLoginSuccess" />
  </div>
</template>

<style scoped lang="less">
//媒体查询，按照不同尺寸引入pc.less 或者mobile.less

@media (min-width: 751px) {
  @import "./pc.less";
}

@media (max-width: 750px) {
  @import "./mobile.less";
}
</style>
