.main {
  width: 7.5rem;
  height: 18.64rem;
  background-color: #ffd6ac;

  .red {
    color: red;
  }

  .stored-value-hall {
    position: absolute;
    width: 7.5rem;
    top: 1.22rem;
    left: 0;
    .p1 {
      position: absolute;
      top: 0rem;
      left: 0rem;
      width: 100%;
      height: 2.38rem;
      background: url("@/assets/imgs/mobile/p1-bg.png") no-repeat;
      background-size: 100% 100%;
      .title {
        position: absolute;
        left: 0.63rem;
        top: 0.12rem;
        font-size: 0.26rem;
        letter-spacing: -1px;
        color: #212121;
        font-weight: bold;
        text-align: center;
      }
      .game-icon {
        position: absolute;
        top: 0.78rem;
        left: 0.25rem;
        width: 0.75rem;
        height: 0.75rem;
      }
      .select-box {
        position: absolute;
        top: 0.85rem;
        left: 1.12rem;
        display: flex;
        flex-wrap: wrap;
        .select-item {
          display: flex;
          align-items: center;
          margin-right: 0.29rem;
          margin-bottom: 0.3rem;
          width: 2.8rem;
          &:nth-child(1) {
            width: 4rem;
          }
          .label {
            margin-right: 0.04rem;
            font-size: 0.18rem;
            color: #212121;
            font-weight: bold;
            text-align: center;
          }
          .game-name {
            font-size: 0.25rem;
            color: #212121;
            font-weight: bold;
            text-align: center;
          }
          :deep(.ant-select) {
            width: 2.1rem;
            .ant-select-selector {
              height: 0.42rem;
              line-height: 0.42rem;
            }
            .ant-select-selection-item {
              line-height: 0.42rem !important;
              display: flex;
              align-items: center;
            }
            .ant-select-selection-placeholder {
              line-height: 0.42rem !important;
              display: flex;
              align-items: center;
            }
            .ant-select-selector {
              border-radius: 0;
            }
          }
        }
      }

      .store-buttons {
        position: absolute;
        top: 0.82em;
        right: 0.6rem;
        display: flex;
        gap: 0.1rem;

        .store-btn {
          display: inline-block;

          img {
            height: 0.42rem;
            width: auto;
          }
        }
      }
    }
    .p2 {
      position: absolute;
      top: 2.41rem;
      left: 0rem;
      width: 100%;
      height: 3.93rem;
      background: url("@/assets/imgs/mobile/p2-bg.png") no-repeat;
      background-size: 100% 100%;
      .title {
        position: absolute;
        top: 0.06rem;
        left: 0.6rem;
        font-size: 0.26rem;
        letter-spacing: 0rem;
        color: #212121;
        font-weight: bold;
        text-align: center;
      }
      .tabs {
        position: absolute;
        top: 0.6rem;
        left: 0.28rem;
        display: flex;
        .tab {
          cursor: pointer;
          width: 1.34rem;
          height: 0.44rem;
          line-height: 0.36rem;
          border: 1px solid rgba(33, 33, 33, 0.3);
          font-size: 0.18rem;
          color: #000000;
          text-align: center;
          border-bottom: none;
          margin-right: 0.03rem;
          &.active {
            background-color: #f4f3f9;
          }
        }
      }
      .p2-content-box {
        position: absolute;
        top: 1rem;
        left: 0.28rem;
        width: 6.8rem;
        height: 2.65rem;
        min-height: 2.2rem;
        background-color: #f4f3f9;
        overflow-x: hidden;
        overflow-y: auto;

        .pay-list {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 0.08rem 0rem;
          margin-top: 0.12rem;
          margin-left: 0.22rem;
          .pay-item {
            width: 1.46rem;
            height: 0.59rem;
            font-size: 0.16rem;
            cursor: pointer;
            position: relative;

            &.active::before {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border: 0.02rem solid #f39800;
              border-radius: 0.04rem;
              pointer-events: none;
              z-index: 1;
            }
          }
        }
      }
      .more-pay {
        position: absolute;
        top: 0.15rem;
        right: 0.23rem;
        font-size: 0.14rem;
        color: #212121;
        cursor: pointer;
      }
      .more {
        position: absolute;
        bottom: 0.32rem;
        right: 0.55rem;
        font-size: 0.16rem;
        color: #2178d2;
        text-align: center;
      }
      .pop-more-pay-box {
        position: absolute;
        top: 0.32rem;
        right: 0.09rem;
        width: 5.45rem;
        height: 3.16rem;
        border-radius: 0.1rem;
        background-color: #ffffff;
        border: 1px solid rgba(33, 33, 33, 0.3);
        z-index: 2;
        padding: 0 0.15rem;
        .pop-area-list {
          display: flex;
          margin-top: 0.11rem;
          .pop-area-item {
            width: 1.67rem;
            height: 0.43rem;
            line-height: 0.43rem;
            font-size: 0.14rem;
            color: #000000;
            text-align: center;
            border: 1px solid #d4d4d4;
            margin-right: 0.08rem;
            &.active {
              background-color: #ff9d33;
            }
          }
        }
        .pop-pay-list {
          width: 5.17rem;
          height: 2.22rem;
          overflow-y: auto;
          display: flex;
          flex-wrap: wrap;
          margin-top: 0.25rem;
          gap: 0.1rem;
          .pop-pay-item {
            margin-right: 0.11rem;
            width: 1.46rem;
            height: 0.6rem;
            background: url("@/assets/imgs/pc/VS.png") no-repeat;
            background-size: 100% 100%;
            cursor: pointer;
            position: relative;

            &.active::before {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border: 0.02rem solid #f39800;
              border-radius: 0.04rem;
              pointer-events: none;
              z-index: 1;
            }
          }
        }
      }
    }
    .p3 {
      position: absolute;
      top: 6.39rem;
      left: 0rem;
      width: 100%;
      height: 6.89rem;
      background: url("@/assets/imgs/mobile/p3-bg.png") no-repeat;
      background-size: 100% 100%;
      .title {
        position: absolute;
        left: 0.68rem;
        top: 0.1rem;
        font-size: 0.25rem;
        color: #212121;
        text-align: center;
      }
      .goods-classify {
        position: absolute;
        top: 0.68rem;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        font-size: 0.25rem;
        letter-spacing: -1px;
        color: #212121;
        &::after {
          content: "";
          position: absolute;
          width: 100%;
          height: 1px;
          bottom: -0.05rem;
          left: 0;
          background-color: #ff9d33;
          z-index: 2;
        }
      }
      .goods-list {
        height: 5.2rem;
        width: 6.29rem;
        position: absolute;
        top: 1.3rem;
        left: 0.6rem;
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 0.1rem 0.16rem;
        overflow-y: auto;
        .goods-item {
          position: relative;
          display: flex;
          width: 6.28rem;
          height: 1.45rem;
          border-radius: 0.05rem;
          background-color: #ffffff;
          border: 1px solid #bcbcbc;
          cursor: pointer;
          &.active {
            border: 2px solid #f39800;
          }
          .goods-img-box {
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 0.03rem;
            background-color: #f4f3f9;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 0.06rem;
            margin-top: 0.12rem;
            .type {
              position: absolute;
              top: 0;
              left: -0.045rem;
              width: 0.85rem;
              height: 0.26rem;
              line-height: 0.26rem;
              font-size: 0.17rem;
              color: #ffffff;
              background-color: #e60012;
              font-weight: 500;
              text-align: center;
              border-radius: 0.1rem;
            }
            .goods-img {
              width: 0.7rem;
              height: 0.64rem;
            }
          }
          .goods-info-box {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0 0.1rem;
            padding-top: 0.2rem;
            .top-box {
              display: flex;
              justify-content: space-between;
              width: 4.7rem;
              .goods-title {
                width: 3rem;
                font-size: 0.31rem;
                color: #212121;
                font-weight: bold;
                text-align: left;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .goods-price {
                font-size: 0.27rem;
                color: #212121;
                font-weight: bold;
                text-align: right;
                white-space: nowrap;
              }
            }
            .goods-desc {
              font-size: 0.23rem;
              line-height: 0.31rem;
              color: #212121;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  .tips-box {
    margin-top: 14rem;
    margin-left: 0.2rem;
    padding-bottom: 1.5rem;

    .pay-desc {
      font-size: 0.16rem;
      line-height: 1.5;
      font-weight: 500;
      text-align: left;
    }

    .declare {
      font-size: 0.14rem;
      line-height: 1.8;
      letter-spacing: 0rem;
      color: #212121;
      .blue-font {
        cursor: pointer;
        color: #2178d2;
      }
    }
  }
}
