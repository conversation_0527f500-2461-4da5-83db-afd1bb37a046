<script setup lang="ts">
import { useAppStore } from "@/stores/app";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const appStore = useAppStore();

const buttonConfig = [
  { type: "fbGroup", class: "societies", textKey: "message.officialFBCommunity", urlField: "facebook_group_url" },
  { type: "fbPage", class: "fanclub", textKey: "message.officialFBPage", urlField: "facebook_profile_url" },
  { type: "discord", class: "dc-community", textKey: "message.discordCommunity", urlField: "discord_group_url" },
  { type: "naver", class: "naver", textKey: "message.naverForum", urlField: "naver_group_url" },
  { type: "apk", class: "apk", textKey: "message.downloadAPK", urlField: "apk_download_url" },
];

function toURL(type: string) {
  if (!appStore.initData) return;

  const button = buttonConfig.find((btn) => btn.type === type);
  if (button && appStore.initData[button.urlField]) {
    window.open(appStore.initData[button.urlField]);
  }
}
</script>

<template>
  <div class="float-right">
    <div v-if="appStore.initData?.apk_qrcode" class="qrcode">
      <img :src="appStore.initData?.apk_qrcode" alt="" />
    </div>
    <div class="btn-box">
      <template v-for="btn in buttonConfig" :key="btn.type">
        <div :class="`${btn.class} btn-item`" @click="toURL(btn.type)" v-if="appStore.initData?.[btn.urlField]">
          {{ t(btn.textKey) }}
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
// PC端样式
@media (min-width: 751px) {
  .float-right {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    width: 163px;
    height: auto;
    background-color: #feb143;
    border: 1px solid #fd8505;
    border-radius: 20px;
    background-size: 100% 100%;

    .qrcode {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 133px;
      height: 133px;
      margin-top: 15px;
      background: url("@/assets/imgs/pc/pop/right/qrcode-bg.png");
      background-size: 100% 100%;

      img {
        width: 113px;
        height: 113px;
        border-radius: 6px;
      }
    }

    .btn-box {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin: 15px 0;
      transform: translateX(-5px);

      .btn-item {
        cursor: pointer;
        width: 173px;
        height: 40px;
        line-height: 38px;
        font-size: 16px;
        color: #ffffff;
        font-weight: bold;
        text-align: center;
      }

      .societies {
        background: url("@/assets/imgs/pc/pop/right/blue1.png");
        background-size: 100% 100%;
      }

      .fanclub {
        background: url("@/assets/imgs/pc/pop/right/blue1.png");
        background-size: 100% 100%;
      }

      .dc-community {
        background: url("@/assets/imgs/pc/pop/right/blue2.png");
        background-size: 100% 100%;
      }

      .naver {
        background: url("@/assets/imgs/pc/pop/right/green.png");
        background-size: 100% 100%;
      }

      .apk {
        background: url("@/assets/imgs/pc/pop/right/red.png");
        background-size: 100% 100%;
      }
    }
  }
}
</style>
