<script setup lang="ts">
import { Dropdown, <PERSON>u, <PERSON>u<PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, <PERSON><PERSON> } from "ant-design-vue";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { DownOutlined } from "@ant-design/icons-vue";
import { useAppStore } from "@/stores/app";

// Props
interface Props {
  selectTabs: number;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  "update:selectTabs": [value: number];
  openLogin: [];
  handleLogout: [];
}>();

const { t, locale } = useI18n();
const appStore = useAppStore();

// 语言配置
const languageOptions = [
  { key: "TW", label: "繁體中文" },
  { key: "CN", label: "简体中文" },
  { key: "EN", label: "English" },
  { key: "JP", label: "日本語" },
  { key: "KR", label: "한국어" },
  { key: "TH", label: "ไทย" },
  { key: "VN", label: "Tiếng Việt" },
];

// 当前语言信息
const currentLanguage = computed(() => {
  return languageOptions.find((lang) => lang.key === locale.value) || languageOptions[0];
});

// 语言切换函数
const changeLanguage = (languageKey: string) => {
  locale.value = languageKey;
  localStorage.setItem("language", languageKey);
};

// 标签切换
const handleTabClick = (tabIndex: number) => {
  emit("update:selectTabs", tabIndex);
};

// 打开登录弹窗
const openLogin = () => {
  emit("openLogin");
};

// 退出登录
const handleLogout = () => {
  emit("handleLogout");
  appStore.setUserInfo(null);
  location.reload();
};
</script>

<template>
  <div class="head">
    <div class="head-block"></div>
    <div class="triangle-down" :class="{ two: selectTabs == 2 }"></div>
    <div class="tabs">
      <div class="tab" @click="handleTabClick(1)">
        <div class="tab-text" :class="{ active: selectTabs == 1 }">
          {{ t("message.paymentCenter") }}
        </div>
      </div>
      <div class="tab" @click="handleTabClick(2)">
        <div class="tab-text" :class="{ active: selectTabs == 2 }">
          {{ t("message.paymentRecords") }}
        </div>
      </div>
    </div>
    <div class="right-box">
      <div class="right-dropdown">
        <Dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            {{ currentLanguage.label }}
            <DownOutlined class="down-icon" />
          </a>
          <template #overlay>
            <Menu class="ant-dropdown-menu">
              <MenuItem v-for="lang in languageOptions" :key="lang.key">
                <a href="javascript:;" @click="changeLanguage(lang.key)">
                  {{ lang.label }}
                </a>
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </div>
      <div v-if="!appStore.userInfo" class="login-box" @click="openLogin">{{ t("message.login") }}/{{ t("message.logout") }}</div>
      <template v-else>
        <Popconfirm :title="t('message.confirmLogout')" :cancelText="t('message.cancel')" :cancelButtonProps="{ danger: true, type: 'primary' }">
          <div class="login-user-info">
            <img src="@/assets/imgs/pc/icon-user.png" alt="" />
            <div class="user-name">{{ appStore.userInfo.user_name }}</div>
          </div>
          <template #okButton>
            <Button type="primary" size="small" @click="handleLogout">{{ t("message.confirm") }}</Button>
          </template>
        </Popconfirm>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
// PC端样式
@media (min-width: 751px) {
  .head {
    min-width: 1500px;
    height: 85px;
    position: relative;
    background-color: white;

    .head-block {
      position: absolute;
      top: 0;
      left: 180px;
      width: 125px;
      height: 85px;
      background: url("@/assets/imgs/pc/head-block.png");
      background-size: 100% 100%;
      pointer-events: none;
    }

    .triangle-down {
      position: absolute;
      bottom: -9px;
      left: 300px;
      width: 0;
      height: 0;
      border-left: 7.5px solid transparent;
      border-right: 7.5px solid transparent;
      border-top: 10px solid #fff;
      &.two {
        left: 472px;
      }
    }

    .tabs {
      position: absolute;
      display: flex;
      justify-content: space-between;
      left: 257px;
      bottom: 13px;
      gap: 30px;

      .tab-text {
        font-size: 25px;
        color: #898989;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
        &.active {
          color: #ff7f02;
        }
      }
    }

    .right-box {
      position: absolute;
      right: 260px;
      bottom: 20px;
      display: flex;
      align-items: center;

      .login-box {
        padding: 0 20px;
        height: 39px;
        line-height: 39px;
        font-size: 24px;
        background-color: #ffffff;
        border: 1px solid black;
        border-radius: 18px;
        text-align: center;
        cursor: pointer;
      }

      .login-user-info {
        display: flex;
        align-items: center;
        font-size: 18px;
        text-align: center;
        cursor: pointer;

        img {
          width: 19px;
          height: 19px;
        }

        .user-name {
          margin-left: 10px;
        }
      }

      .right-dropdown {
        margin-right: 17px;
        display: flex;
        align-items: center;
        cursor: pointer;

        .ant-dropdown-link {
          opacity: 0.8;
          font-size: 20px;
          color: #2178d2;
          text-align: center;
        }
      }
    }
  }
}

// 移动端样式
@media (max-width: 750px) {
  .head {
    width: 7.5rem;
    height: 0.74rem;
    position: relative;
    background-color: white;

    .triangle-down {
      position: absolute;
      bottom: -0.07rem;
      left: 0.5rem;
      width: 0;
      height: 0;
      border-left: 0.1rem solid transparent;
      border-right: 0.1rem solid transparent;
      border-top: 0.1rem solid #fff;
      &.two {
        left: 2.3rem;
      }
    }

    .tabs {
      position: absolute;
      display: flex;
      justify-content: space-between;
      left: 0.15rem;
      bottom: 0.13rem;
      gap: 0.1rem;

      .tab-text {
        font-size: 0.25rem;
        color: #898989;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
        &.active {
          color: #ff7f02;
        }
      }
    }

    .right-box {
      position: absolute;
      right: 0.16rem;
      display: flex;
      height: 100%;
      align-items: center;

      .login-box {
        padding: 0 0.2rem;
        height: 0.39rem;
        line-height: 0.39rem;
        font-size: 0.24rem;
        background-color: #ffffff;
        border: 1px solid black;
        border-radius: 0.18rem;
        text-align: center;
        cursor: pointer;
      }

      .login-user-info {
        display: flex;
        align-items: center;
        font-size: 0.24rem;
        text-align: center;
        cursor: pointer;

        img {
          width: 0.19rem;
          height: 0.19rem;
        }

        .user-name {
          margin-left: 0.1rem;
        }
      }

      .right-dropdown {
        display: flex;
        align-items: center;
        margin-right: 0.17rem;

        .ant-dropdown-link {
          opacity: 0.8;
          font-size: 0.24rem;
          text-align: center;
        }

        .down-icon {
          font-size: 0.18rem;
        }
      }
    }
  }
}
</style>
