<script setup lang="ts">
import { Pagination } from "ant-design-vue";
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import AppApi, { type IPayRecordItem, type IPayRecordParams } from "@/api/AppApi";
import { ApiResultCode } from "@/api/viewModel/IApiResultCode";
import { useAppStore } from "@/stores/app";

const { t } = useI18n();
const appStore = useAppStore();

// 支付记录数据
const payRecordList = ref<IPayRecordItem[]>([]);

// 分页参数
const current = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取支付记录
async function getPayRecords() {
  try {
    if (!appStore.userInfo) {
      return;
    }

    const params: IPayRecordParams = {
      user_id: appStore.userInfo.user_id,
      page: current.value.toString(),
      page_size: pageSize.value.toString(),
    };

    const res = await AppApi.getPayRecord(params);
    if (res.code === ApiResultCode.OK && res.data) {
      payRecordList.value = res.data.data || [];
      total.value = res.data.total;
    }
  } catch (error) {
    console.error("获取支付记录失败:", error);
  }
}

// 分页变化处理
const handlePageChange = (page: number) => {
  current.value = page;
  getPayRecords();
};

watch(
  () => appStore.userInfo,
  () => getPayRecords(),
  { immediate: true }
);
</script>

<template>
  <div class="recharge-record">
    <div class="hint">{{ t("message.paymentHistoryNote") }}</div>
    <div class="record-box">
      <div class="list">
        <div class="item" v-for="item in payRecordList" :key="item.order_id">
          <div class="game-name item-item">
            <div class="label">{{ t("message.gameName") }}：</div>
            <div class="value">{{ item.user_side_display_name }}</div>
          </div>

          <div class="order-time item-item">
            <div class="label">{{ t("message.orderTime") }}：</div>
            <div class="value">{{ item.pay_time }}</div>
          </div>

          <div class="order-id item-item">
            <div class="label">{{ t("message.orderID") }}：</div>
            <div class="value">{{ item.order_id }}</div>
          </div>

          <div class="goods-name item-item">
            <div class="label">{{ t("message.itemName") }}：</div>
            <div class="value">{{ item.product_name }}</div>
          </div>

          <div class="order-price item-item">
            <div class="label">{{ t("message.orderAmount") }}：</div>
            <div class="value">{{ item.money }} {{ item.currency }}</div>
          </div>

          <div class="payment-method item-item">
            <div class="label">{{ t("message.paymentMethod") }}：</div>
            <div class="value">{{ item.channel_name }}</div>
          </div>
        </div>

        <!-- 无数据时显示提示 -->
        <div v-if="payRecordList.length === 0" class="no-data">
          {{ t("message.noPaymentRecords") }}
        </div>
      </div>
    </div>

    <Pagination class="pagination" v-model:current="current" v-model:page-size="pageSize" :total="total" show-less-items @change="handlePageChange" />
  </div>
</template>

<style scoped lang="less">
// PC端样式
@media (min-width: 751px) {
  .recharge-record {
    position: relative;
    width: 1440px;
    margin: 0 auto;

    .hint {
      margin-top: 20px;
      margin-left: 20px;
      font-size: 18px;
      color: #ff0000;
    }

    .record-box {
      height: 806px;
      margin-top: 20px;
      border-radius: 10px;
      filter: drop-shadow(0 0 3.5px rgba(181, 106, 0, 0.45));
      background-color: #ffffff;
      padding-bottom: 76px;

      .list {
        font-size: 15px;
        color: #2a292a;
        height: 720px;
        overflow-y: auto;

        .item {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          height: 178px;
          background-color: #ffffff;
          border-bottom: 1px solid #c4c4c4;
          margin: 0 39px;
          padding: 5px 0;

          .item-item {
            display: flex;
            margin-left: 3px;

            .label {
              font-weight: bold;
            }
          }
        }

        .no-data {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
          font-size: 16px;
          color: #999;
        }
      }
    }

    .pagination {
      position: absolute;
      bottom: 24px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

// 移动端样式
@media (max-width: 750px) {
  .recharge-record {
    position: relative;
    width: 90%;
    margin: 0 auto;

    .hint {
      position: absolute;
      top: 0.43rem;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 0.18rem;
      color: #ff0000;
    }

    .record-box {
      width: 100%;
      height: 13.08rem;
      position: absolute;
      top: 0.75rem;
      left: 0rem;
      border-radius: 0.1rem;
      filter: drop-shadow(0 0 0.035rem rgba(181, 106, 0, 0.45));
      background-color: #ffffff;
      padding-bottom: 1.64rem;

      .list {
        padding-top: 0.23rem;
        font-size: 0.18rem;
        color: #2a292a;
        height: 11.42rem;
        overflow-y: auto;

        .item {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          height: 2.24rem;
          background-color: #ffffff;
          border-bottom: 1px solid #c4c4c4;
          margin: 0 0.39rem;
          padding: 0.05rem 0;

          .item-item {
            display: flex;
            margin-left: 0.03rem;

            .label {
              font-weight: bold;
            }
          }
        }

        .no-data {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
          font-size: 16px;
          color: #999;
        }
      }
    }

    .pagination {
      position: absolute;
      top: 12.91rem;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
