.main {
  min-width: 1440px;
  height: 1330px;
  background: url("@/assets/imgs/pc/bg.png") no-repeat;
  background-size: cover;

  .red {
    color: red;
  }

  .stored-value-hall {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 20px;
    gap: 20px;

    .p1 {
      position: relative;
      width: 1423px;
      height: 132px;
      background: url("@/assets/imgs/pc/p1-bg.png") no-repeat;
      background-size: 100% 100%;
      .title {
        position: absolute;
        left: 41px;
        top: 12px;
        font-size: 18px;
        letter-spacing: 0px;
        color: #212121;
        font-weight: bold;
        text-align: center;
      }
      .game-icon {
        position: absolute;
        top: 58px;
        left: 38px;
        width: 50px;
        height: 50px;
      }
      .select-box {
        position: absolute;
        top: 65px;
        left: 112px;
        display: flex;
        .select-item {
          display: flex;
          align-items: center;
          margin-right: 29px;
          .label {
            margin-right: 4px;
            font-size: 14px;
            color: #212121;
            font-weight: bold;
            text-align: center;
          }
          .game-name {
            font-size: 18px;
            color: #212121;
            font-weight: bold;
            text-align: center;
          }
          :deep(.ant-select) {
            width: 145px;
            .ant-select-selector {
              height: 42px;
              line-height: 42px;
            }
            .ant-select-selection-item {
              line-height: 42px !important;
              display: flex;
              align-items: center;
            }
            .ant-select-selection-placeholder {
              line-height: 42px !important;
              display: flex;
              align-items: center;
            }
          }
        }
      }

      .store-buttons {
        position: absolute;
        top: 65px;
        right: 41px;
        display: flex;
        gap: 10px;

        .store-btn {
          display: inline-block;

          img {
            height: 42px;
            width: auto;
          }

          &.apple-store img {
            height: 42px;
          }

          &.google-play img {
            height: 42px;
          }
        }
      }
    }
    .p2 {
      position: relative;
      width: 1423px;
      height: 316px;
      background: url("@/assets/imgs/pc/p2-bg.png") no-repeat;
      background-size: 100% 100%;
      .title {
        position: absolute;
        left: 41px;
        top: 6px;
        font-size: 18px;
        letter-spacing: 0rem;
        color: #212121;
        font-weight: bold;
        text-align: center;
      }
      .tabs {
        position: absolute;
        top: 51px;
        left: 109px;
        display: flex;
        .tab {
          cursor: pointer;
          width: 232px;
          height: 36px;
          line-height: 36px;
          border: 1px solid rgba(33, 33, 33, 0.3);
          font-size: 14px;
          color: #000000;
          text-align: center;
          border-bottom: none;
          margin-right: 9px;
          &.active {
            background-color: #f4f3f9;
          }
        }
      }
      .p2-content-box {
        position: absolute;
        top: 86px;
        left: 33px;
        width: 1350px;
        height: 200px;
        background-color: #f4f3f9;
        overflow-x: hidden;
        overflow-y: auto;

        .pay-list {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          gap: 10px;
          padding: 20px;
          margin: 0 auto;
          width: 1314px;
          .pay-item {
            width: 145px;
            height: 60px;
            font-size: 14px;
            cursor: pointer;
            position: relative;

            &.active::before {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border: 2px solid #f39800;
              border-radius: 4px;
              pointer-events: none;
              z-index: 1;
            }
          }
        }
      }
      .more-pay {
        position: absolute;
        top: 8px;
        right: 10px;
        font-size: 14px;
        color: #212121;
        cursor: pointer;
      }
      .more {
        position: absolute;
        bottom: 50px;
        right: 55px;
        font-size: 16px;
        color: #2178d2;
        text-align: center;
      }
      .pop-more-pay-box {
        position: absolute;
        top: 32px;
        right: 9px;
        width: 545px;
        height: 316px;
        border-radius: 10px;
        background-color: #ffffff;
        border: 1px solid rgba(33, 33, 33, 0.3);
        z-index: 2;
        padding: 0 15px;
        .pop-area-list {
          display: flex;
          margin-top: 11px;
          .pop-area-item {
            width: 167px;
            height: 43px;
            line-height: 43px;
            font-size: 14px;
            color: #000000;
            text-align: center;
            border: 1px solid #d4d4d4;
            margin-right: 8px;
            &.active {
              background-color: #ff9d33;
            }
          }
        }
        .pop-pay-list {
          width: 517px;
          height: 222px;
          overflow-y: auto;
          display: flex;
          flex-wrap: wrap;
          margin-top: 25px;
          gap: 10px;
          .pop-pay-item {
            margin-right: 11px;
            width: 146px;
            height: 60px;
            background: url("@/assets/imgs/pc/VS.png") no-repeat;
            background-size: 100% 100%;
            cursor: pointer;
            position: relative;

            &.active::before {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border: 2px solid #f39800;
              border-radius: 4px;
              pointer-events: none;
              z-index: 1;
            }
          }
        }
      }
    }
    .p3 {
      position: relative;
      width: 1423px;
      height: 322px;
      background: url("@/assets/imgs/pc/p3-bg.png") no-repeat;
      background-size: 100% 100%;
      .title {
        position: absolute;
        left: 41px;
        top: 13px;
        font-size: 18px;
        letter-spacing: 0rem;
        color: #212121;
        font-weight: bold;
        text-align: center;
      }
      .goods-classify {
        position: absolute;
        top: 55px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        font-size: 24px;
        letter-spacing: -1px;
        color: #212121;

        &::after {
          content: "";
          position: absolute;
          width: 100%;
          height: 1px;
          bottom: -5px;
          left: 0;
          background-color: #ff9d33;
          z-index: 2;
        }
      }
      .goods-list {
        position: absolute;
        top: 110px;
        left: 35px;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px 16px;
        height: 185px;
        overflow-y: auto;
        .goods-item {
          position: relative;
          display: flex;
          align-items: center;
          width: 322px;
          height: 72px;
          border-radius: 5px;
          background-color: #ffffff;
          border: 1px solid #bcbcbc;
          cursor: pointer;
          &.active {
            border: 2px solid #f39800;
          }
          .goods-img-box {
            width: 61px;
            height: 61px;
            border-radius: 3px;
            background-color: #f4f3f9;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 6px;
            .type {
              position: absolute;
              top: 0;
              left: -1px;
              width: 60px;
              height: 16px;
              background-color: #e60012;
              border-radius: 5px;
              font-size: 12px;
              line-height: 16px;
              color: #fff;
              text-align: center;
            }
            .goods-img {
              width: 36px;
              height: 33px;
            }
          }
          .goods-info-box {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0 10px;
            .top-box {
              display: flex;
              justify-content: space-between;
              .goods-title {
                width: 150px;
                font-size: 16px;
                color: #212121;
                font-weight: bold;
                text-align: left;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .goods-price {
                font-size: 14px;
                line-height: 16px;
                color: #212121;
                font-weight: bold;
                text-align: right;
                white-space: nowrap;
              }
            }
            .goods-desc {
              font-size: 12px;
              line-height: 16px;
              color: #212121;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  .tips-box {
    width: 1423px;
    margin: 20px auto 0;
    padding-bottom: 130px;

    .pay-desc {
      margin-bottom: 40px;
      font-size: 16px;
      line-height: 1.5;
      font-weight: 500;
      text-align: left;
    }

    .declare {
      font-size: 14px;
      line-height: 1.8;
      letter-spacing: 0rem;
      color: #212121;
      .blue-font {
        cursor: pointer;
        color: #2178d2;
      }
    }
  }
}
