<script setup lang="ts">
import { RouterView } from "vue-router";
import LoadingSpinner from "@/components/LoadingSpinner.vue";
import { useAppStore } from "./stores/app";
import { storeToRefs } from "pinia";

const appStore = useAppStore();
const { isShowLoading } = storeToRefs(appStore);
</script>

<template>
  <div class="app">
    <LoadingSpinner v-if="isShowLoading" />
    <RouterView />
  </div>
</template>

<style lang="less" scoped></style>
