// src/hooks/useAnalytics.ts
import { getCommonGa4Config } from "@/views/platformConfig";
import { onMounted } from "vue";
import { useRoute } from "vue-router";

export type AnalyticsChannel = "meta" | "tiktok" | "ga4Utm" | "ga4";

export interface ChannelConfig {
  type: AnalyticsChannel;
  id: string;
}

export interface PlatformMap {
  [key: string]: {
    channel: AnalyticsChannel;
    id: string;
    params?: any;
    eventName: string;
    ios_cpp_link?: string;
    gp_link?: string;
  };
}

export function useAnalytics(pixelMap: PlatformMap) {
  const route = useRoute();

  // 渠道初始化映射
  const initializers: Record<AnalyticsChannel, (id: string) => void> = {
    meta: initMetaPixel,
    tiktok: initTikTokPixel,
    ga4Utm: initGoogleAnalytics,
    ga4: initGoogleAnalytics,
  };

  // Meta Pixel初始化
  function initMetaPixel(pixelId: string) {
    if (!window.fbq) {
      (function (f: any, b: any, e: any) {
        var n = f.fbq;
        if (n) return n.callMethod;
        n = f.fbq = function () {
          n.callMethod
            ? n.callMethod.apply(n, arguments)
            : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        var t = b.createElement(e);
        t.async = !0;
        t.src = "https://connect.facebook.net/en_US/fbevents.js";
        var s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, "script");
      // Initialize Facebook Pixel with the provided pixel ID
      window.fbq("init", pixelId);
      window.fbq("track", "PageView");
    }
  }

  // TikTok Pixel初始化
  function initTikTokPixel(pixelId: string) {
    window.TiktokAnalyticsObject = "ttq";
    const ttq = (window.ttq = window.ttq || []);

    // 方法队列初始化
    const methods = [
      "page",
      "track",
      "identify",
      "instances",
      "debug",
      "on",
      "off",
      "once",
      "ready",
      "alias",
      "group",
      "enableCookie",
      "disableCookie",
      "holdConsent",
      "revokeConsent",
      "grantConsent",
    ];

    methods.forEach((method) => {
      ttq[method] = (...args: any) => ttq.push([method, ...args]);
    });

    // 动态加载脚本
    if (!document.querySelector(`script[src*="analytics.tiktok.com"]`)) {
      const script = document.createElement("script");
      script.src = `https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=${pixelId}&lib=ttq`;
      document.head.appendChild(script);
    }

    ttq.page(); // 触发页面跟踪
  }

  // GA4初始化
  function initGoogleAnalytics(ga4Id: string) {
    const script = document.createElement("script");
    script.src = `https://www.googletagmanager.com/gtag/js?id=${ga4Id}`;
    script.async = true;
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    if (!window.gtag) {
      window.gtag = function () {
        window.dataLayer.push(arguments);
      };
    }
    window.gtag("js", new Date());
    window.gtag("config", ga4Id, {
      send_to: ga4Id,
    });
  }

  // 统一事件跟踪
  const trackEvent = (
    type: AnalyticsChannel,
    eventName: string,
    params: Record<string, any> = {}
  ) => {
    switch (type) {
      case "meta":
        window.fbq?.("track", eventName, params);
        break;
      case "tiktok":
        window.ttq?.track(eventName, params);
        break;
      case "ga4Utm":
        window.gtag?.("event", eventName, params);
        break;
      case "ga4":
        window.gtag?.("event", eventName, params);
        break;
    }
  };

  // 初始化渠道
  const initializeChannels = () => {
    //初始化ga4跟踪代码
    const ga4Config = getCommonGa4Config();
    if (ga4Config) {
      initGoogleAnalytics(ga4Config.id);
    }
    const page = route.name as string;
    const utm = ((route.params?.utm as string) || "").toLowerCase();
    if (!utm) {
      return;
    }
    const channelConfig = pixelMap[`${page}_${utm}`];
    if (channelConfig) {
      initializers[channelConfig.channel](channelConfig.id);
    }
  };

  const getChannelConfig = () => {
    const page = route.name as string;
    const utm = ((route.params?.utm as string) || "").toLowerCase();
    if (!utm) {
      return;
    }
    let config = pixelMap[`${location.host}_${page}_${utm}`];
    if (!config) {
      config = pixelMap[`${page}_${utm}`];
    }
    return config;
  };

  onMounted(() => {
    initializeChannels();
  });

  return {
    trackEvent,
    getChannelConfig,
  };
}
