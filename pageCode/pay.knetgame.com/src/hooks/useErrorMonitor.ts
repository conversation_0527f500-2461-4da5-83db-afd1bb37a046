import { onMounted, onUnmounted } from "vue";

interface ErrorData {
  type: string;
  row?: number;
  col?: number;
  msg: any;
  url?: string;
  time: string;
}

interface Options {
  webhookUrl: string;
}

const useErrorMonitor = (
  options: Options = {
    webhookUrl:
      "https://open.feishu.cn/open-apis/bot/v2/hook/418b152e-f32d-4723-84d6-345300019275",
  }
) => {
  const config = { ...options };

  const handleJsError = (event: any) => {
    try {
      const errorData: ErrorData = {
        type: "javascript",
        msg: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          error: event.error,
        },
        time: getTime(),
      };
      uploadMonitorErrors(errorData);
    } catch (error) {}
  };

  const handlePromiseError = (e: PromiseRejectionEvent) => {
    try {
      const errorData: any = {
        type: "promise",
        msg: e.reason?.message || e.reason || "",
        time: getTime(),
      };
      uploadMonitorErrors(errorData);
    } catch (error) {}
  };

  const uploadMonitorErrors = (errorData: ErrorData) => {
    aPost("web_error", errorData);
  };

  const aPost = (eventName: string, eventValue: any) => {
    try {
      const body = JSON.stringify({
        msg_type: "text",
        content: {
          text: `前端监控告警:${JSON.stringify({
            eventName,
            eventValue: {
              ...eventValue,
              url: location.href,
            },
          })}`,
        },
      });

      fetch(config.webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body,
      }).catch((err) => console.error("上报失败:", err));
    } catch (error) {}
  };

  onMounted(() => {
    window.addEventListener("error", handleJsError as any);
    window.addEventListener("unhandledrejection", handlePromiseError);
  });

  onUnmounted(() => {
    window.removeEventListener("error", handleJsError as any);
    window.removeEventListener("unhandledrejection", handlePromiseError);
  });

  return { uploadMonitorErrors };
};

const getTime = (): string => {
  return new Date().toISOString();
};

export default useErrorMonitor;
