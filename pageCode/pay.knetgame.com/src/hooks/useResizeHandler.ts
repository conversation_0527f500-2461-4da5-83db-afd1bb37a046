import {
  onMounted,
  onBeforeUnmount,
  ref,
  nextTick,
  type Ref,
  watch,
} from "vue";
import { useIsMobile } from "@/hooks/useIsMobile";

export function useResizeHandler(
  mobile: number[] = [750, 750],
  pc: number[] = [1920, 1920]
) {
  const { isMobile } = useIsMobile();

  const maxWidth = ref(isMobile.value ? mobile[0] : pc[0]);
  const pageWidth = ref(isMobile.value ? mobile[1] : pc[1]);
  const fontSize = ref(100); // 存储当前的font-size

  const resizeHandler = () => {
    const docEl = document.documentElement;
    const clientWidth = docEl.clientWidth;

    if (!clientWidth) return;
    fontSize.value =
      100 * (Math.min(clientWidth, maxWidth.value) / pageWidth.value);
    docEl.style.fontSize = fontSize.value + "px";
  };

  onMounted(async () => {
    window.addEventListener("resize", resizeHandler);
    await nextTick(); // 确保DOM更新
    resizeHandler(); // 初次加载时立即执行
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", resizeHandler);
  });

  watch(isMobile, (value) => {
    if (value) {
      maxWidth.value = mobile[0];
      pageWidth.value = mobile[1];
    } else {
      maxWidth.value = pc[0];
      pageWidth.value = pc[1];
    }
  });

  const getFixedValue = (value: number) => {
    return (value * fontSize.value) / 100; // 根据当前font-size计算固定值
  };

  return { getFixedValue, triggerResize: resizeHandler };
}
