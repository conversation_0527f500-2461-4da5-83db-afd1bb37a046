import { onBeforeUnmount, onMounted, ref } from "vue";

export function useIsMobile() {
  const size = 750;
  const isMobile = ref(window.innerWidth <= size);

  // 判断屏幕是否是手机端
  const handleResize = () => {
    isMobile.value = window.innerWidth <= size;
  };

  // 监听页面尺寸变化
  onMounted(() => {
    window.addEventListener("resize", handleResize);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", handleResize);
  });

  return { isMobile };
}
