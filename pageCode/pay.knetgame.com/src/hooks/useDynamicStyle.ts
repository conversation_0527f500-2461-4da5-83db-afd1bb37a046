import { onMounted, watch } from "vue";
import { useIsMobile } from "./useIsMobile";

/**
 * 动态 import less 样式，根据终端类型自动加载 pc.less 或 mobile.less
 * 可在任意组件中调用
 */
export function useDynamicStyle() {
  const { isMobile } = useIsMobile();

  // 根据终端类型动态 import 样式
  const loadStyle = async () => {
    if (isMobile.value) {
      console.log("加载移动端样式");
      await import("../views/mobile.less");
    } else {
      console.log("加载 PC 端样式");
      await import("../views/pc.less");
    }
  };

  onMounted(() => {
    loadStyle();
  });

  watch(isMobile, () => {
    loadStyle();
  });
}
