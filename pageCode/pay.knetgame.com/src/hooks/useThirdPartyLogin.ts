import { ref } from "vue";
import { message } from "ant-design-vue";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/stores/app";
import AppApi from "@/api/AppApi";
import { ApiResultCode } from "@/api/viewModel/IApiResultCode";

export function useThirdPartyLogin() {
  const { t } = useI18n();
  const appStore = useAppStore();
  const isLoading = ref(false);

  // 缓存标志，防止重复初始化
  const isInitialized = ref(false);
  const initPromise = ref<Promise<void> | null>(null);

  /**
   * 获取配置信息
   */
  const getConfig = () => {
    const initData = appStore.initData;
    if (!initData?.official_payment_config) {
      throw new Error("Payment configuration not found in initData");
    }

    const { facebook_app_id, google_client_id } = initData.official_payment_config;

    if (!facebook_app_id) {
      throw new Error("Facebook App ID not configured");
    }

    if (!google_client_id) {
      throw new Error("Google Client ID not configured");
    }

    return {
      facebookAppId: facebook_app_id,
      googleClientId: google_client_id,
    };
  };

  /**
   * 通用的 script 加载函数
   */
  const loadScript = (
    src: string,
    options: {
      async?: boolean;
      defer?: boolean;
      crossOrigin?: string;
      onLoad?: () => void;
    } = {}
  ) => {
    return new Promise<void>((resolve, reject) => {
      // 创建新的 script 标签
      const script = document.createElement("script");
      script.src = src;
      script.async = options.async ?? true;
      script.defer = options.defer ?? true;
      if (options.crossOrigin) {
        script.crossOrigin = options.crossOrigin;
      }

      script.onload = () => {
        options.onLoad?.();
        resolve();
      };
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));

      document.head.appendChild(script);
    });
  };

  /**
   * 统一初始化第三方登录 SDK
   */
  const initThirdPartySDKs = async () => {
    // 如果已经初始化过，直接返回
    if (isInitialized.value) {
      return;
    }

    // 如果正在初始化中，返回现有的 Promise
    if (initPromise.value) {
      return initPromise.value;
    }

    // 创建新的初始化 Promise
    initPromise.value = (async () => {
      try {
        const initPromises: Promise<void>[] = [];

        // 初始化 Google Identity Services
        if (!window.google?.accounts) {
          initPromises.push(loadScript("https://accounts.google.com/gsi/client"));
        }

        // 初始化 Facebook SDK
        if (!window.FB) {
          const config = getConfig();
          initPromises.push(
            loadScript("https://connect.facebook.net/en_US/sdk.js", {
              crossOrigin: "anonymous",
              onLoad: () => {
                // 初始化 Facebook SDK
                window.FB?.init({
                  appId: config.facebookAppId,
                  cookie: true,
                  xfbml: true,
                  version: "v18.0",
                });
              },
            })
          );
        }

        // 等待所有 SDK 初始化完成
        if (initPromises.length > 0) {
          await Promise.all(initPromises);
        }

        // 标记为已初始化
        isInitialized.value = true;
        console.log("Third party SDKs initialized successfully");
      } catch (error) {
        // 初始化失败，重置状态允许重试
        console.error("Failed to initialize third party SDKs:", error);
        isInitialized.value = false;
        initPromise.value = null;
        throw error;
      }
    })();

    return initPromise.value;
  };

  /**
   * 重置初始化状态（用于测试或重新初始化）
   */
  const resetInitialization = () => {
    isInitialized.value = false;
    initPromise.value = null;
  };

  /**
   * Google 登录
   */
  const handleGoogleLogin = async () => {
    try {
      isLoading.value = true;

      await initThirdPartySDKs();

      if (!window.google?.accounts) {
        throw new Error("Google Identity Services not loaded");
      }

      const config = getConfig();
      const client = window.google.accounts.oauth2.initTokenClient({
        client_id: config.googleClientId,
        scope: "email profile",
        ux_mode: "popup",
        callback: async (response: any) => {
          if (response.error) {
            throw new Error(response.error);
          }

          // 调用后端登录接口
          await loginWithThirdParty({
            provider: "google",
            accessToken: response.access_token,
          });
        },
      });

      client.requestAccessToken();
    } catch (error: any) {
      console.error("Google login error:", error);
      message.error(error.message || "Google 登录失败");
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Facebook 登录
   */
  const handleFacebookLogin = async () => {
    try {
      isLoading.value = true;

      await initThirdPartySDKs();

      const facebookLogin = () =>
        new Promise<any>((resolve) => {
          if (!window.FB) {
            throw new Error("Facebook SDK not loaded");
          }
          // 发起 Facebook 登录
          window.FB.login(
            (response: any) => {
              resolve(response);
              isLoading.value = false;
            },
            {
              scope: "email,public_profile",
              return_scopes: true,
            }
          );
        });

      const res = await facebookLogin();

      // 调用后端登录接口;
      await loginWithThirdParty({
        provider: "facebook",
        accessToken: res?.authResponse?.accessToken,
      });
    } catch (error: any) {
      console.error("Facebook login error:", error);
      message.error(error.message || "Facebook 登录失败");
      isLoading.value = false;
    }
  };

  /**
   * 调用后端第三方登录接口
   */
  const loginWithThirdParty = async (loginData: { provider: string; accessToken?: string }) => {
    try {
      let result: any;

      // 根据 provider 类型调用相应的 API 方法
      if (loginData.provider === "google") {
        // Google 登录
        if (!loginData.accessToken) {
          throw new Error("Google accessToken is required");
        }

        result = await AppApi.googleLogin({
          access_token: loginData.accessToken,
        });
      } else if (loginData.provider === "facebook") {
        // Facebook 登录 - 使用 access token
        if (!loginData.accessToken) {
          throw new Error("Facebook access token is required");
        }

        result = await AppApi.facebookLogin({
          access_token: loginData.accessToken,
        });
      } else {
        throw new Error(`Unsupported provider: ${loginData.provider}`);
      }

      // 验证 API 响应
      if (!result) {
        throw new Error("No response from server");
      }

      // 处理登录成功后的逻辑
      if (result.code === ApiResultCode.OK) {
        appStore.setUserInfo(result.data);
      } else {
        // 处理登录失败的情况
        const errorMessage = result.message || `${loginData.provider} 登录失败`;
        throw new Error(errorMessage);
      }

      return result;
    } catch (error) {
      console.error("Third party login API error:", error);
    }
  };

  return {
    isLoading,
    handleGoogleLogin,
    handleFacebookLogin,
    initThirdPartySDKs,
    resetInitialization,
  };
}
