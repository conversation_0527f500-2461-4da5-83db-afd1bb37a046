import { onMounted } from "vue";

export function usePreloadAssets(assets?: string[]) {
  onMounted(() => {
    // 如果没有传入 assets，则使用 import.meta.glob 获取 assets 目录下的图片和视频文件
    const assetsToPreload = assets
      ? assets
      : Object.values(
          import.meta.glob("/src/assets/imgs/**/*.{jpg,png,svg,mp4}")
        );

    const preloadAssets = () => {
      assetsToPreload.forEach(async (loadAsset) => {
        // 动态加载资源并根据文件类型进行处理
        if (typeof loadAsset === "function") {
          const module = (await loadAsset()) as { default: string };
          const fileUrl = module.default;

          if (fileUrl.endsWith(".mp4")) {
            // 如果是视频文件
            const video = document.createElement("video");
            video.src = fileUrl;
            video.preload = "auto";
          } else {
            // 如果是图片文件
            const img = new Image();
            img.src = fileUrl;
          }
        } else {
          // 直接传入的资源 URL（处理图片或视频）
          if (loadAsset.endsWith(".mp4")) {
            const video = document.createElement("video");
            video.src = loadAsset;
            video.preload = "auto";
          } else {
            const img = new Image();
            img.src = loadAsset;
          }
        }
      });
    };

    // 使用 requestIdleCallback，如果浏览器不支持，则使用 setTimeout 作为后备方案
    if ("requestIdleCallback" in window) {
      requestIdleCallback(preloadAssets);
    } else {
      setTimeout(preloadAssets, 3000);
    }
  });
}
